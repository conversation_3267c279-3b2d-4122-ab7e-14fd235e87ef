import 'package:account_management/application/period_tracking_watcher_bloc/period_tracking_watcher_bloc.dart';
import 'package:auto_route/auto_route.dart';
import 'package:design_system/design/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../custom_widgets/dotted_border_widget.dart';
import 'package:account_management/account_management.dart';
import '../../helpers.dart';

@RoutePage()
class PeriodTrackingInsightsPage extends StatelessWidget {
  const PeriodTrackingInsightsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<ManagePeriodTrackingBloc>(
            create: (context) => getIt<ManagePeriodTrackingBloc>()),
        BlocProvider<PeriodTrackingWatcherBloc>(
          create: (context) => getIt<PeriodTrackingWatcherBloc>()
            ..add(PeriodTrackingWatcherEvent.watchAllStarted()),
        ),
      ],
      child: PeriodTrackingInsightsScaffold(),
    );
  }
}

class PeriodTrackingInsightsScaffold extends StatefulWidget {
  const PeriodTrackingInsightsScaffold({super.key});

  @override
  State<PeriodTrackingInsightsScaffold> createState() =>
      _PeriodTrackingInsightsScaffoldState();
}

class _PeriodTrackingInsightsScaffoldState
    extends State<PeriodTrackingInsightsScaffold> {
  bool _isEditMode = false;
  late PageController _pageController;

  // Cache for expensive calculations
  Map<String, bool> _datePropertiesCache = {};
  Map<String, Widget> _widgetCache = {};
  List<List<DateTime>> _periodCyclesCache = [];
  List<List<DateTime>> _ovulationCyclesCache = [];
  Set<DateTime> _lastProcessedPeriodDates = {};
  Set<DateTime> _lastProcessedOvulationDates = {};

  // Pre-computed lookup sets for O(1) date checking
  Set<String> _periodDateKeys = {};
  Set<String> _ovulationDateKeys = {};
  DateTime? _todayNormalized;

  @override
  void initState() {
    super.initState();
    _pageController = PageController(
      initialPage: 12, // Start at current month
      viewportFraction: 0.4, // Show 3 cards at once
    );
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  // Generate list of months to display (past 1 year + current month + future 6 months)
  List<DateTime> _getMonthsToDisplay() {
    final now = DateTime.now();
    List<DateTime> months = [];

    // Add past 12 months
    for (int i = 12; i >= 1; i--) {
      months.add(DateTime(now.year, now.month - i, 1));
    }

    // Add current month
    months.add(DateTime(now.year, now.month, 1));

    // Add future 6 months
    for (int i = 1; i <= 6; i++) {
      months.add(DateTime(now.year, now.month + i, 1));
    }

    return months;
  }

  // Clear cache when data changes
  void _clearCache() {
    _datePropertiesCache.clear();
    _widgetCache.clear();
    _periodCyclesCache.clear();
    _ovulationCyclesCache.clear();
    _lastProcessedPeriodDates.clear();
    _lastProcessedOvulationDates.clear();
    _periodDateKeys.clear();
    _ovulationDateKeys.clear();
  }

  // Generate cache key for date properties
  String _getCacheKey(DateTime date, String property) {
    return '${date.year}-${date.month}-${date.day}_$property';
  }

  // Generate date key for O(1) lookups
  String _getDateKey(DateTime date) {
    return '${date.year}-${date.month}-${date.day}';
  }

  // Update lookup sets for fast date checking
  void _updateLookupSets(
      Set<DateTime> periodDates, Set<DateTime> ovulationDates) {
    // Update period date keys
    if (_lastProcessedPeriodDates.length != periodDates.length ||
        !_lastProcessedPeriodDates.containsAll(periodDates)) {
      _periodDateKeys = periodDates.map(_getDateKey).toSet();
    }

    // Update ovulation date keys
    if (_lastProcessedOvulationDates.length != ovulationDates.length ||
        !_lastProcessedOvulationDates.containsAll(ovulationDates)) {
      _ovulationDateKeys = ovulationDates.map(_getDateKey).toSet();
    }

    // Update today's date
    final today = DateTime.now();
    _todayNormalized = DateTime(today.year, today.month, today.day);
  }

  // Optimized method to update period cycles cache
  void _updatePeriodCyclesCache(Set<DateTime> periodDates) {
    if (_lastProcessedPeriodDates.length == periodDates.length &&
        _lastProcessedPeriodDates.containsAll(periodDates)) {
      return; // Cache is still valid
    }

    _lastProcessedPeriodDates = Set.from(periodDates);
    _periodCyclesCache.clear();

    if (periodDates.isEmpty) return;

    List<DateTime> sortedPeriodDates = periodDates.toList()..sort();
    List<DateTime> currentCycle = [];

    for (int i = 0; i < sortedPeriodDates.length; i++) {
      if (currentCycle.isEmpty) {
        currentCycle.add(sortedPeriodDates[i]);
      } else {
        DateTime lastDate = currentCycle.last;
        int daysDifference = sortedPeriodDates[i].difference(lastDate).inDays;

        if (daysDifference <= 1) {
          currentCycle.add(sortedPeriodDates[i]);
        } else {
          _periodCyclesCache.add(List.from(currentCycle));
          currentCycle = [sortedPeriodDates[i]];
        }
      }
    }
    if (currentCycle.isNotEmpty) {
      _periodCyclesCache.add(currentCycle);
    }
  }

  // Helper method to check if a date is in a period window (continuous flow between period dates)
  bool _isInPeriodWindow(DateTime date, Set<DateTime> periodDates) {
    final cacheKey = _getCacheKey(date, 'periodWindow');
    if (_datePropertiesCache.containsKey(cacheKey)) {
      return _datePropertiesCache[cacheKey]!;
    }

    _updatePeriodCyclesCache(periodDates);
    bool result = false;

    // Check if date falls within any period cycle
    for (List<DateTime> cycle in _periodCyclesCache) {
      if (cycle.length >= 3) {
        DateTime startDate = cycle.first;
        DateTime endDate = cycle.last;

        if (date.isAfter(startDate) &&
            date.isBefore(endDate) &&
            !cycle.any((periodDate) =>
                periodDate.year == date.year &&
                periodDate.month == date.month &&
                periodDate.day == date.day)) {
          result = true;
          break;
        }
      }
    }

    _datePropertiesCache[cacheKey] = result;
    return result;
  }

  // Helper method to check if a date is the first date in a period cycle
  bool _isFirstPeriodDate(DateTime date, Set<DateTime> periodDates) {
    final cacheKey = _getCacheKey(date, 'firstPeriod');
    if (_datePropertiesCache.containsKey(cacheKey)) {
      return _datePropertiesCache[cacheKey]!;
    }

    _updatePeriodCyclesCache(periodDates);
    bool result = false;

    // Check if date is the first date in any cycle
    for (List<DateTime> cycle in _periodCyclesCache) {
      DateTime firstDate = cycle.first;
      if (firstDate.year == date.year &&
          firstDate.month == date.month &&
          firstDate.day == date.day) {
        result = true;
        break;
      }
    }

    _datePropertiesCache[cacheKey] = result;
    return result;
  }

  // Helper method to check if a date is the last date in a period cycle
  bool _isLastPeriodDate(DateTime date, Set<DateTime> periodDates) {
    final cacheKey = _getCacheKey(date, 'lastPeriod');
    if (_datePropertiesCache.containsKey(cacheKey)) {
      return _datePropertiesCache[cacheKey]!;
    }

    _updatePeriodCyclesCache(periodDates);
    bool result = false;

    // Check if date is the last date in any cycle
    for (List<DateTime> cycle in _periodCyclesCache) {
      DateTime lastDate = cycle.last;
      if (lastDate.year == date.year &&
          lastDate.month == date.month &&
          lastDate.day == date.day) {
        result = true;
        break;
      }
    }

    _datePropertiesCache[cacheKey] = result;
    return result;
  }

  // Optimized method to update ovulation cycles cache
  void _updateOvulationCyclesCache(Set<DateTime> ovulationDates) {
    if (_lastProcessedOvulationDates.length == ovulationDates.length &&
        _lastProcessedOvulationDates.containsAll(ovulationDates)) {
      return; // Cache is still valid
    }

    _lastProcessedOvulationDates = Set.from(ovulationDates);
    _ovulationCyclesCache.clear();

    if (ovulationDates.isEmpty) return;

    List<DateTime> sortedOvulationDates = ovulationDates.toList()..sort();
    List<DateTime> currentCycle = [];

    for (int i = 0; i < sortedOvulationDates.length; i++) {
      if (currentCycle.isEmpty) {
        currentCycle.add(sortedOvulationDates[i]);
      } else {
        DateTime lastDate = currentCycle.last;
        int daysDifference =
            sortedOvulationDates[i].difference(lastDate).inDays;

        if (daysDifference <= 1) {
          currentCycle.add(sortedOvulationDates[i]);
        } else {
          _ovulationCyclesCache.add(List.from(currentCycle));
          currentCycle = [sortedOvulationDates[i]];
        }
      }
    }
    if (currentCycle.isNotEmpty) {
      _ovulationCyclesCache.add(currentCycle);
    }
  }

  // Helper method to check if a date is in an ovulation window (fertile window around ovulation)
  bool _isInOvulationWindow(DateTime date, Set<DateTime> ovulationDates) {
    final cacheKey = _getCacheKey(date, 'ovulationWindow');
    if (_datePropertiesCache.containsKey(cacheKey)) {
      return _datePropertiesCache[cacheKey]!;
    }

    bool result = false;
    for (DateTime ovulationDate in ovulationDates) {
      final difference = date.difference(ovulationDate).inDays.abs();
      if (difference <= 2 && difference > 0) {
        result = true;
        break;
      }
    }

    _datePropertiesCache[cacheKey] = result;
    return result;
  }

  // Helper method to check if a date is the first date in an ovulation cycle
  bool _isFirstOvulationDate(DateTime date, Set<DateTime> ovulationDates) {
    final cacheKey = _getCacheKey(date, 'firstOvulation');
    if (_datePropertiesCache.containsKey(cacheKey)) {
      return _datePropertiesCache[cacheKey]!;
    }

    _updateOvulationCyclesCache(ovulationDates);
    bool result = false;

    // Check if date is the first date in any cycle
    for (List<DateTime> cycle in _ovulationCyclesCache) {
      DateTime firstDate = cycle.first;
      if (firstDate.year == date.year &&
          firstDate.month == date.month &&
          firstDate.day == date.day) {
        result = true;
        break;
      }
    }

    _datePropertiesCache[cacheKey] = result;
    return result;
  }

  // Helper method to check if a date is the last date in an ovulation cycle
  bool _isLastOvulationDate(DateTime date, Set<DateTime> ovulationDates) {
    final cacheKey = _getCacheKey(date, 'lastOvulation');
    if (_datePropertiesCache.containsKey(cacheKey)) {
      return _datePropertiesCache[cacheKey]!;
    }

    _updateOvulationCyclesCache(ovulationDates);
    bool result = false;

    // Check if date is the last date in any cycle
    for (List<DateTime> cycle in _ovulationCyclesCache) {
      DateTime lastDate = cycle.last;
      if (lastDate.year == date.year &&
          lastDate.month == date.month &&
          lastDate.day == date.day) {
        result = true;
        break;
      }
    }

    _datePropertiesCache[cacheKey] = result;
    return result;
  }

  Widget _buildScaledMonthCard(
      DateTime month, PeriodTrackingWatcherState state, int index) {
    return AnimatedBuilder(
      animation: _pageController,
      builder: (context, child) {
        double value = 1.0;
        if (_pageController.position.haveDimensions) {
          value = _pageController.page! - index;
          value = (1 - (value.abs() * 0.2)).clamp(0.8, 1.0);
        }

        return Center(
          child: Transform.scale(
            scale: Curves.easeOut.transform(value),
            child: Opacity(
              opacity: value,
              child: Container(
                height: 500.h, // Fixed height for all cards
                child: _buildMonthCalendar(month, state),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildMonthCalendar(DateTime month, PeriodTrackingWatcherState state) {
    return RepaintBoundary(
      child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 4.h),
          child: Container(
              margin: EdgeInsets.symmetric(vertical: 4.h, horizontal: 8.w),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.2),
                    spreadRadius: 2,
                    blurRadius: 5,
                    offset: Offset(0, 3),
                  ),
                ],
              ),
              child: Padding(
                padding: EdgeInsets.only(
                    top: 12.h, left: 12.w, right: 12.w, bottom: 12.h),
                child: TableCalendar<DateTime>(
                  firstDay: DateTime.utc(2020, 1, 1),
                  lastDay: DateTime.utc(2030, 12, 31),
                  focusedDay: month,
                  calendarFormat: CalendarFormat.month,
                  startingDayOfWeek: StartingDayOfWeek.sunday,
                  availableGestures:
                      AvailableGestures.none, // Disable all swipe gestures
                  sixWeekMonthsEnforced: false, // Allow variable height
                  headerStyle: HeaderStyle(
                    formatButtonVisible: false,
                    titleCentered: true,
                    leftChevronVisible: false,
                    rightChevronVisible: false,
                    headerMargin: EdgeInsets.only(bottom: 8.h),
                    titleTextStyle: GoogleFonts.roboto(
                      fontSize: 24.sp,
                      fontWeight: FontWeight.w400,
                      color: Color(0xff6C618B),
                    ),
                  ),
                  daysOfWeekStyle: DaysOfWeekStyle(
                    weekdayStyle: GoogleFonts.roboto(
                      fontSize: 20.sp,
                      fontWeight: FontWeight.w500,
                      color: Color(0xff71456F),
                    ),
                    weekendStyle: GoogleFonts.roboto(
                      fontSize: 20.sp,
                      fontWeight: FontWeight.w500,
                      color: Color(0xff71456F),
                    ),
                  ),
                  onDaySelected: _isEditMode
                      ? (selectedDay, focusedDay) {
                          // Only allow editing past and current dates
                          final today = DateTime.now();
                          final selectedDateOnly = DateTime(selectedDay.year,
                              selectedDay.month, selectedDay.day);
                          final todayOnly =
                              DateTime(today.year, today.month, today.day);

                          if (selectedDateOnly.isAfter(todayOnly)) {
                            // Future date - show message and don't allow selection
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('Cannot edit future dates'),
                                backgroundColor: Colors.orange,
                              ),
                            );
                            return;
                          }

                          // Clear widget cache when user makes changes
                          _widgetCache.clear();

                          // Toggle period date selection
                          context.read<ManagePeriodTrackingBloc>().add(
                              ManagePeriodTrackingEvent.selectDay(
                                  selectedDay, true));
                        }
                      : null,
                  calendarStyle: CalendarStyle(
                    outsideDaysVisible: false,
                    cellMargin: EdgeInsets.all(2.w),
                    defaultTextStyle: GoogleFonts.roboto(
                      fontSize: 20.sp,
                      fontWeight: FontWeight.w400,
                      color: Color(0xff71456F),
                    ),
                    weekendTextStyle: GoogleFonts.roboto(
                      fontSize: 20.sp,
                      fontWeight: FontWeight.w400,
                      color: Color(0xff71456F),
                    ),
                  ),
                  calendarBuilders: CalendarBuilders(
                    defaultBuilder: (context, date, focusedDay) {
                      return RepaintBoundary(
                        child: _buildDayCell(date, state),
                      );
                    },
                    todayBuilder: (context, date, focusedDay) {
                      return RepaintBoundary(
                        child: _buildDayCell(date, state, isToday: true),
                      );
                    },
                    outsideBuilder: (context, date, focusedDay) {
                      return Container(); // Hide outside days
                    },
                  ),
                ),
              ))),
    );
  }

  Widget _buildDayCell(DateTime date, PeriodTrackingWatcherState state,
      {bool isToday = false}) {
    return state.maybeMap(
      data: (dataState) {
        // Generate widget cache key
        final dateKey = _getDateKey(date);
        final widgetCacheKey =
            '${dateKey}_${dataState.selectedDays.length}_${dataState.ovulationDays.length}';

        // Return cached widget if available
        if (_widgetCache.containsKey(widgetCacheKey)) {
          return _widgetCache[widgetCacheKey]!;
        }

        // Update lookup sets for fast O(1) checking
        _updateLookupSets(dataState.selectedDays, dataState.ovulationDays);

        final dateNormalized = DateTime(date.year, date.month, date.day);

        // Fast O(1) date property checks
        bool isTodayDate = dateNormalized.isAtSameMomentAs(_todayNormalized!);
        bool isFutureDate = dateNormalized.isAfter(_todayNormalized!);
        bool isSelectedPeriodDate = _periodDateKeys.contains(dateKey);
        bool isOvulationDate = _ovulationDateKeys.contains(dateKey);

        // Only compute expensive properties if needed
        bool isFirstPeriodDate = false;
        bool isLastPeriodDate = false;
        bool isInPeriodWindow = false;
        bool isFirstOvulationDate = false;
        bool isLastOvulationDate = false;
        bool isInOvulationWindow = false;

        if (isSelectedPeriodDate) {
          isFirstPeriodDate = _isFirstPeriodDate(date, dataState.selectedDays);
          isLastPeriodDate = _isLastPeriodDate(date, dataState.selectedDays);
        } else {
          isInPeriodWindow = _isInPeriodWindow(date, dataState.selectedDays);
        }

        if (isOvulationDate) {
          isFirstOvulationDate =
              _isFirstOvulationDate(date, dataState.ovulationDays);
          isLastOvulationDate =
              _isLastOvulationDate(date, dataState.ovulationDays);
        } else {
          isInOvulationWindow =
              _isInOvulationWindow(date, dataState.ovulationDays);
        }

        // Build widget
        final widget = _buildDayWidget(
          date: date,
          isTodayDate: isTodayDate,
          isFutureDate: isFutureDate,
          isSelectedPeriodDate: isSelectedPeriodDate,
          isFirstPeriodDate: isFirstPeriodDate,
          isLastPeriodDate: isLastPeriodDate,
          isInPeriodWindow: isInPeriodWindow,
          isOvulationDate: isOvulationDate,
          isFirstOvulationDate: isFirstOvulationDate,
          isLastOvulationDate: isLastOvulationDate,
          isInOvulationWindow: isInOvulationWindow,
        );

        // Cache the widget
        _widgetCache[widgetCacheKey] = widget;
        return widget;
      },
      orElse: () => Container(
        width: 45.w,
        height: 45.h,
        child: Center(
          child: Text(
            '${date.day}',
            style: GoogleFonts.roboto(
              color: Color(0xff71456F),
              fontWeight: FontWeight.w400,
              fontSize: 20.sp,
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<ManagePeriodTrackingBloc, ManagePeriodTrackingState>(
      listener: (context, state) {
        state.maybeWhen(
          dataLoaded: () {
            // Period dates saved successfully
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Period dates updated successfully!'),
                backgroundColor: Colors.green,
              ),
            );
            setState(() {
              _isEditMode = false;
            });
          },
          orElse: () {},
        );
      },
      child: Scaffold(
        backgroundColor: Color(0xffFAF2DF),
        appBar: AppBar(
          title: Text('Period Insights'),
          backgroundColor: Color(0xffFAF2DF),
          elevation: 0,
        ),
        body:
            BlocBuilder<PeriodTrackingWatcherBloc, PeriodTrackingWatcherState>(
          builder: (context, state) {
            return state.maybeMap(
              data: (dataState) {
                final months = _getMonthsToDisplay();
                return Column(
                  children: [
                    Expanded(
                      child: PageView.builder(
                        scrollDirection: Axis.vertical,
                        controller: _pageController,
                        itemCount: months.length,
                        itemBuilder: (context, index) {
                          return _buildScaledMonthCard(
                              months[index], state, index);
                        },
                      ),
                    ),
                    // Edit button at the bottom
                    Padding(
                      padding: EdgeInsets.all(16.w),
                      child: GestureDetector(
                        onTap: () {
                          if (_isEditMode) {
                            // Save changes and exit edit mode
                            context.read<ManagePeriodTrackingBloc>().add(
                                  ManagePeriodTrackingEvent
                                      .savePeriodDatesAndRecalculate(),
                                );
                            setState(() {
                              _isEditMode = false;
                            });
                            // Clear cache when exiting edit mode
                            _widgetCache.clear();
                          } else {
                            // Enter edit mode
                            setState(() {
                              _isEditMode = true;
                            });
                            // Clear cache when entering edit mode
                            _widgetCache.clear();
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                    'Tap on dates to select/deselect period days'),
                                backgroundColor: AppTheme.primaryColor,
                              ),
                            );
                          }
                        },
                        child: Container(
                          height: 45.h,
                          width: 100.w,
                          decoration: BoxDecoration(
                            color: AppTheme.primaryColor,
                            borderRadius: BorderRadius.circular(22.5),
                          ),
                          child: Center(
                            child: Text(
                              _isEditMode ? 'Done' : 'Edit',
                              style: GoogleFonts.roboto(
                                color: Colors.white,
                                fontWeight: FontWeight.w500,
                                fontSize: 16.sp,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                );
              },
              orElse: () => Center(child: CircularProgressIndicator()),
            );
          },
        ),
      ),
    );
  }

  // Main widget builder that uses modular helper methods
  Widget _buildDayWidget({
    required DateTime date,
    required bool isTodayDate,
    required bool isFutureDate,
    required bool isSelectedPeriodDate,
    required bool isFirstPeriodDate,
    required bool isLastPeriodDate,
    required bool isInPeriodWindow,
    required bool isOvulationDate,
    required bool isFirstOvulationDate,
    required bool isLastOvulationDate,
    required bool isInOvulationWindow,
  }) {
    // Priority order: Today > Period dates > Ovulation dates > Windows > Regular

    if (isTodayDate) {
      return _buildTodayDate(date);
    }

    // Period dates (first/last have priority over middle)
    if (isFirstPeriodDate) {
      return _buildPeriodFirstDate(date, isFutureDate);
    }
    if (isLastPeriodDate) {
      return _buildPeriodLastDate(date, isFutureDate);
    }
    if (isSelectedPeriodDate) {
      return _buildPeriodMiddleDate(date, isFutureDate);
    }

    // Ovulation dates (first/last have priority over middle)
    if (isFirstOvulationDate) {
      return _buildOvulationFirstDate(date, isFutureDate);
    }
    if (isLastOvulationDate) {
      return _buildOvulationLastDate(date, isFutureDate);
    }
    if (isOvulationDate) {
      return _buildOvulationMiddleDate(date, isFutureDate);
    }

    // Regular date
    return _buildRegularDate(date);
  }

  // Modular helper methods for better code organization

  Widget _buildTodayDate(DateTime date) {
    return Container(
      width: 45.w,
      height: 45.h,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(color: Color(0xff5DADE2), width: 2),
      ),
      child: Center(
        child: Text(
          '${date.day}',
          style: GoogleFonts.roboto(
            color: Color(0xff5DADE2),
            fontWeight: FontWeight.w500,
            fontSize: 20.sp,
          ),
        ),
      ),
    );
  }

  Widget _buildPeriodFirstDate(DateTime date, bool isFuture) {
    return Container(
      width: 80.w,
      height: 45.h,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          // Light purple extension to the right
          Positioned(
            left: 12.0.w,
            top: 0,
            child: Container(
              width: 80.w,
              height: 45.h,
              decoration: BoxDecoration(
                color: isFuture ? Color(0xffE4DEFF) : Color(0xffCFB4FE),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(22.5.h),
                  bottomLeft: Radius.circular(22.5.h),
                ),
              ),
            ),
          ),
          // Circle centered
          Center(
            child: isFuture
                ? DottedBorder(
                    color: Color(0xff584294),
                    strokeWidth: 1.5,
                    child: _buildRegularDate(date),
                  )
                : _buildCircleContent(date, Color(0xff584294), Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildPeriodLastDate(DateTime date, bool isFuture) {
    return Container(
      width: 100.w,
      height: 55.h,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          // Light purple extension to the left
          Positioned(
            right: 15.w,
            top: 0,
            child: Container(
              width: 100.w,
              height: 55.h,
              decoration: BoxDecoration(
                color: isFuture ? Color(0xffE4DEFF) : Color(0xffCFB4FE),
                borderRadius: BorderRadius.only(
                  topRight: Radius.circular(27.5.h),
                  bottomRight: Radius.circular(27.5.h),
                ),
              ),
            ),
          ),
          // Circle centered
          Center(
            child: isFuture
                ? DottedBorder(
                    color: Color(0xff584294),
                    strokeWidth: 1.5,
                    child: _buildRegularDate(date),
                  )
                : _buildCircleContent(date, Color(0xff584294), Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildPeriodMiddleDate(DateTime date, bool isFuture) {
    return Container(
      width: 104.w,
      height: 55.h,
      decoration: BoxDecoration(
        color: isFuture ? Color(0xffE4DEFF) : Color(0xffCFB4FE),
      ),
      child: Center(
        child: Text(
          '${date.day}',
          style: GoogleFonts.roboto(
            color: isFuture ? Color(0xff71456F) : Colors.white,
            fontWeight: FontWeight.w500,
            fontSize: 25.sp,
          ),
        ),
      ),
    );
  }

  Widget _buildOvulationFirstDate(DateTime date, bool isFuture) {
    return Container(
      width: 100.w,
      height: 55.h,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          // Light purple extension to the right
          Positioned(
            left: 15.0.w,
            top: 0,
            child: Container(
              width: 100.w,
              height: 55.h,
              decoration: BoxDecoration(
                color: isFuture ? Color(0xffFFECC7) : Color(0xffFFDA94),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(27.5.h),
                  bottomLeft: Radius.circular(27.5.h),
                ),
              ),
            ),
          ),
          // Circle centered
          Center(
            child: _buildRegularDate(date),
          ),
        ],
      ),
    );
  }

  Widget _buildOvulationLastDate(DateTime date, bool isFuture) {
    return Container(
      width: 100.w,
      height: 55.h,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          // Light purple extension to the right
          Positioned(
            right: 15.0.w,
            top: 0,
            child: Container(
              width: 100.w,
              height: 55.h,
              decoration: BoxDecoration(
                color: isFuture ? Color(0xffFFECC7) : Color(0xffFFDA94),
                borderRadius: BorderRadius.only(
                  topRight: Radius.circular(27.5.h),
                  bottomRight: Radius.circular(27.5.h),
                ),
              ),
            ),
          ),
          // Circle centered
          Center(
            child: _buildRegularDate(date),
          ),
        ],
      ),
    );
  }

  Widget _buildOvulationMiddleDate(DateTime date, bool isFuture) {
    return Container(
      width: 104.w,
      height: 55.h,
      decoration: BoxDecoration(
        color: isFuture ? Color(0xffFFECC7) : Color(0xffFFDA94),
      ),
      child: Center(
        child: Text(
          '${date.day}',
          style: GoogleFonts.roboto(
            color: Color(0xff71456F),
            fontWeight: FontWeight.w500,
            fontSize: 25.sp,
          ),
        ),
      ),
    );
  }

  Widget _buildCircleContent(
      DateTime date, Color backgroundColor, Color textColor) {
    return Container(
      width: 60.w,
      height: 60.h,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: backgroundColor,
      ),
      child: Center(
        child: Text(
          '${date.day}',
          style: GoogleFonts.roboto(
            color: textColor,
            fontWeight: FontWeight.w500,
            fontSize: 25.sp,
          ),
        ),
      ),
    );
  }

  Widget _buildRegularDate(DateTime date) {
    return Container(
      width: 55.w,
      height: 55.h,
      child: Center(
        child: Text(
          '${date.day}',
          style: GoogleFonts.roboto(
            color: Color(0xff71456F),
            fontWeight: FontWeight.w400,
            fontSize: 25.sp,
          ),
        ),
      ),
    );
  }
}
