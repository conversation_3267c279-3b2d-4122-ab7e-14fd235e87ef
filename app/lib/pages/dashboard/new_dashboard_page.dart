import 'dart:math';

import 'package:design_system/design_system.dart';

import 'package:auto_route/auto_route.dart';
import 'package:design_system/design/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:intl/intl.dart';
import 'package:juno_plus/pages/dashboard/therapy_analytics_chart_page.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:juno_plus/pages/dashboard/menstrual_cycle_dial.dart';
import 'package:juno_plus/pages/medications/medication_button.dart';
import 'package:juno_plus/routing/app_pages.gr.dart';
import 'package:showcaseview/showcaseview.dart';
import 'package:analytics/analytics.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../custom_widgets/curved_app_bar.dart';
import '../connectivity/connectivity_service.dart';
import '../therapy/therapy_feedback_bottom_sheet.dart';

//global variable to be deleted
bool isShowcaseStarted = false;

@RoutePage()
class NewDashboardPage extends StatefulWidget {
  @override
  State<NewDashboardPage> createState() => _NewDashboardPageState();
}

class _NewDashboardPageState extends State<NewDashboardPage> {
  @override
  Widget build(BuildContext context) {
    return ShowCaseWidget(
        enableAutoScroll: true,
        enableShowcase: false,
        builder: (context) {
          return DashboardScaffold();
        });
  }
}

class DashboardScaffold extends StatefulWidget {
  const DashboardScaffold({super.key});

  @override
  State<DashboardScaffold> createState() => _DashboardScaffoldState();
}

class _DashboardScaffoldState extends State<DashboardScaffold> {
  final String formattedDate =
      DateFormat('EEEE, MMMM d\'\'th').format(DateTime.now());

  final connectivityService = ConnectivityService();

  @override
  void initState() {
    super.initState();

    // WidgetsBinding.instance.addPostFrameCallback((_) {
    //   if (!isShowcaseStarted) {
    //     ShowCaseWidget.of(context)!.startShowCase([
    //       _wholeCalendarKey,
    //       // _currerntDayExplanationKey,
    //
    //       // _menstrualCycleDialKey,
    //        _painTrackingKey,
    //       _helpCenterKey,
    //       // _lastUsedSettingKey,
    //        _medicationButtonKey,
    //       _calendarButtonKey,
    //     ]);
    //     isShowcaseStarted = true;
    //   }
    // });
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<bool>(
        stream: connectivityService.connectivityStream,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          return Scaffold(
            extendBodyBehindAppBar: true,
            appBar: CurvedAppBar(
              appBarColor: AppTheme.primaryColor,
              logoColor: Color(0xffFAF2DF),
              height: .35.sw,
              topRightIcon: GestureDetector(
                onTap: () {
                  context.router.push(NotificationsRoute());
                },
                child: Container(
                  height: 40,
                  width: 40,
                  decoration: BoxDecoration(
                    color: Color(0xffFAF2DF),
                    shape: BoxShape.circle,
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(6.0),
                    child: Icon(
                      Icons.notifications_rounded,
                      color: Color(0xff30285D),
                    ),
                  ),
                ),
              ),
            ),
            body: Stack(
              children: [
                // Gradient background
                Positioned.fill(
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Color(0xffFBF0D5), // Light cream
                          Color(0xffF8EEFF), // Light purple
                        ],
                      ),
                    ),
                  ),
                ),
                // Main content
                SingleChildScrollView(
                  child: Column(
                    children: [
                      SizedBox(height: .43.sw),
                      // Date Header
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 24.0),
                        child: Column(
                          children: [
                            Text(
                              DateFormat('EEEE, MMMM d').format(DateTime.now()),
                              style: Theme.of(context)
                                  .textTheme
                                  .headlineSmall!
                                  .copyWith(
                                    color: AppTheme.primaryColor,
                                    fontWeight: FontWeight.w600,
                                    fontSize: 24,
                                  ),
                            ),
                            SizedBox(height: 4),
                            Container(
                              height: 2,
                              width: 60,
                              color: AppTheme.primaryColor,
                            ),
                          ],
                        ),
                      ),

                      SizedBox(height: 10),

                      // Menstrual Cycle Dial Section
                      GestureDetector(
                        key: Key('open_calendar_container'),
                        onTap: () {
                          context.router.push(PeriodTrackingCalendarRoute());
                        },
                        child: Container(
                          margin: EdgeInsets.symmetric(horizontal: 24),
                          padding: EdgeInsets.all(24),
                          child: Column(
                            children: [
                              Container(
                                child: MenstrualCycleDial(),
                              ),
                              SizedBox(height: .1.sw),
                              Container(
                                width: double.infinity,
                                padding: EdgeInsets.symmetric(
                                    vertical: 12, horizontal: 20),
                                decoration: BoxDecoration(
                                  color: Color(0xffE8E0FF),
                                  borderRadius: BorderRadius.circular(25),
                                  //box-shadow: 0px 4px 4px 0px #00000040;
                                  boxShadow: [
                                    BoxShadow(
                                      color: Color(0x40000000),
                                      blurRadius: 4.0,
                                      offset: Offset(0, 1),
                                    ),
                                  ],
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    SvgPicture.asset(
                                      'assets/settings/period.svg',
                                      height: 20,
                                      width: 20,
                                      colorFilter: ColorFilter.mode(
                                          AppTheme.primaryColor,
                                          BlendMode.srcIn),
                                    ),
                                    SizedBox(width: 8),
                                    Text(
                                      'Log Your Symptom',
                                      style: TextStyle(
                                        color: AppTheme.primaryColor,
                                        fontWeight: FontWeight.w500,
                                        fontSize: 16,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),

                      SizedBox(height: 10),

                      // Cards Grid
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 24.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Left Column
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // Help Center Card
                                  GestureDetector(
                                    onTap: () {
                                      context.router
                                          .push(HelpCenterHomeRoute());
                                    },
                                    child: Container(
                                      width: double.infinity,
                                      padding: EdgeInsets.all(16),
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius: BorderRadius.circular(32),
                                        boxShadow: [
                                          BoxShadow(
                                            color: Colors.black
                                                .withValues(alpha: 0.1),
                                            blurRadius: 8,
                                            offset: Offset(0, 2),
                                          ),
                                        ],
                                      ),
                                      child: Row(
                                        children: [
                                          Container(
                                            width: 32,
                                            height: 32,
                                            decoration: BoxDecoration(
                                              color: Color(0xffE8E0FF),
                                              shape: BoxShape.circle,
                                            ),
                                            child: 
                                            Padding(
                                              padding: const EdgeInsets.all(6.0),
                                            child: SvgPicture.asset(
                                              'assets/home/<USER>',
                                              height: 20,
                                              width: 20,
                                              colorFilter: ColorFilter.mode(
                                                  AppTheme.primaryColor,
                                                  BlendMode.srcIn),
                                            ),
                                            ),
                                          ),
                                          SizedBox(width: 12),
                                          Text(
                                            'Help Center',
                                            style: TextStyle(
                                              color: Color(0xff2D2D2D),
                                              fontSize: 16,
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),

                                  SizedBox(height: 16),

                                  GestureDetector(
                                    onTap: () {
                                      context.router
                                          .push(TherapyAnalyticsChartRoute());
                                    },
                                    child: Container(
                                      width: .42.sw,
                                      height: .65.sw,
                                      decoration: BoxDecoration(
                                          color: Colors.white,
                                          boxShadow: [
                                            BoxShadow(
                                              color: Color(0x40000000),
                                              // #00000040 in CSS corresponds to 0x40000000 in Flutter
                                              blurRadius: 4.0,
                                              // the blur radius
                                              offset: Offset(0,
                                                  1), // the x,y offset of the shadow
                                            ),
                                          ],
                                          borderRadius: BorderRadius.all(
                                              Radius.circular(32))),
                                      child: Padding(
                                        padding: const EdgeInsets.all(15.0),
                                        child: Column(
                                          mainAxisAlignment:
                                              MainAxisAlignment.start,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          children: [
                                            BlocBuilder<TherapyAnalyticsBloc,
                                                TherapyAnalyticsState>(
                                              builder: (context, state) {
                                                return state.when(
                                                  initial: () => Container(),
                                                  loading: () => Container(),
                                                  loaded: (chartData, startDate,
                                                      endDate, viewType) {
                                                    // Check if there's any valid therapy data
                                                    if (chartData.isEmpty)
                                                      return Container();

                                                    final validSessions = chartData
                                                        .where((data) =>
                                                            data.tensLevel > 0 ||
                                                            data.heatLevel >
                                                                0 ||
                                                            (data.painLevelBefore !=
                                                                    null &&
                                                                data.painLevelBefore! >
                                                                    0) ||
                                                            (data.painLevelAfter !=
                                                                    null &&
                                                                data.painLevelAfter! >
                                                                    0))
                                                        .toList();

                                                    if (validSessions.isEmpty)
                                                      return Container();

                                                    return Text(
                                                      'Your Therapy',
                                                      style: Theme.of(context)
                                                          .textTheme
                                                          .bodyMedium!
                                                          .copyWith(
                                                            color: Color(
                                                                0xff30285D),
                                                          ),
                                                    );
                                                  },
                                                  error: (message) =>
                                                      Container(),
                                                );
                                              },
                                            ),
                                            SizedBox(
                                              height: 0,
                                            ),
                                            Container(
                                                height: .50.sw,
                                                child:
                                                    TherapyAnalyticsChartMiniWidget()),
                                      
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),

                            SizedBox(width: 16),

                            // Right Column
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  // Last Used Setting Card
                              Container(
                                width: .38.sw,
                                height: .54.sw,
                                decoration: BoxDecoration(
                                    color: Colors.white,
                                    boxShadow: [
                                      BoxShadow(
                                        color: Color(
                                            0x40000000), // #00000040 in CSS corresponds to 0x40000000 in Flutter
                                        blurRadius: 4.0, // the blur radius
                                        offset: Offset(
                                            0, 1), // the x,y offset of the shadow
                                      ),
                                    ],
                                    borderRadius:
                                    BorderRadius.all(Radius.circular(32))),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    SizedBox(
                                      height: 10,
                                    ),
                                    Text(
                                      'Last Used\nSetting',
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodyMedium!
                                          .copyWith(
                                        color: Color(0xff30285D),
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                    SizedBox(
                                      height: 5,
                                    ),
                                    Container(
                                      padding: EdgeInsets.all(5),
                                      decoration: BoxDecoration(
                                          color: Color(0xffFAF2DF),
                                          borderRadius: BorderRadius.all(
                                              Radius.circular(32))),
                                      height: .34.sw,
                                      width: .28.sw,
                                      child: Column(
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                        children: [
                                          Column(
                                            children: [
                                              Row(
                                                mainAxisAlignment:
                                                MainAxisAlignment.center,
                                                children: [
                                                  Text(
                                                    '4',
                                                    style: Theme.of(context)
                                                        .textTheme
                                                        .bodyMedium!
                                                        .copyWith(
                                                      color: Color(0xff30285D),
                                                    ),
                                                  ),
                                                  SizedBox(
                                                    width: 5,
                                                  ),
                                                  SvgPicture.asset(
                                                    'assets/remote/remote_heat.svg',
                                                    height: 30,
                                                    width: 30,
                                                  ),
                                                ],
                                              ),
                                              SizedBox(
                                                height: 6,
                                              ),
                                              Text(
                                                'Heat',
                                                style: Theme.of(context)
                                                    .textTheme
                                                    .bodyMedium!
                                                    .copyWith(
                                                  color: Color(0xff30285D),
                                                ),
                                              ),
                                            ],
                                          ),
                                          SizedBox(
                                            height: 10,
                                          ),
                                          Column(
                                            children: [
                                              Row(
                                                mainAxisAlignment:
                                                MainAxisAlignment.center,
                                                children: [
                                                  Text(
                                                    '7',
                                                    style: Theme.of(context)
                                                        .textTheme
                                                        .bodyMedium!
                                                        .copyWith(
                                                      color: Color(0xff30285D),
                                                    ),
                                                  ),
                                                  SizedBox(
                                                    width: 5,
                                                  ),
                                                  SvgPicture.asset(
                                                    'assets/remote/remote_charge.svg',
                                                    height: 25,
                                                    width: 27,
                                                  ),
                                                ],
                                              ),
                                              SizedBox(
                                                height: 6,
                                              ),
                                              Text(
                                                'Electric',
                                                style: Theme.of(context)
                                                    .textTheme
                                                    .bodyMedium!
                                                    .copyWith(
                                                  color: Color(0xff30285D),
                                                ),
                                              ),
                                            ],
                                          ),
                                          
                                        ],
                                      ),
                                    ),
                                    SizedBox(
                                            height: 20,
                                          ),
                                  ],
                                ),
                              ),
                          

                                  SizedBox(height: 16),

                                  // Medication Card
              
                                   MedicationButton(),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),

                      SizedBox(height: 140),
                    ],
                  ),
                ),
              ], // <-- Close Stack children
            ),
            // floatingActionButton: FloatingActionButton(
            //   onPressed: () => _showTherapyFeedbackTest(context),
            //   child: const Icon(Icons.feedback),
            //   tooltip: 'Test Therapy Feedback',
            // ),
          );
        });
  }
}
